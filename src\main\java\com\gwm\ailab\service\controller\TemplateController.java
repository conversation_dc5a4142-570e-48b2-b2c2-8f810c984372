package com.gwm.ailab.service.controller;

import com.alibaba.fastjson2.JSONObject;
import com.gwm.ailab.service.common.ResponseResult;
import com.gwm.ailab.service.service.CardTemplateService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * 模板控制器
 *
 * <AUTHOR> Lab GW00295473
 * @version 1.0.0
 */
@Slf4j
@RestController
@RequestMapping("/template")
@Validated
@Tag(name = "服务卡片模板管理", description = "卡片模板相关的API接口")
@RequiredArgsConstructor
public class TemplateController {

    private final CardTemplateService cardTemplateService;

    /**
     * 卡片模板同步接口
     *
     * @param json 模板同步请求参数
     * @return ResponseResult<Void> 同步结果，成功返回200状态码
     * <AUTHOR> Lab GW00295473
     * @since 1.0.0
     */
    @PostMapping("/manual/sync")
    @Operation(
            summary = "卡片模板手动同步接口",
            description = "同步卡片模板到Redis缓存中"
    )
    public ResponseResult<Void> templateSync(@RequestBody JSONObject json) {
        cardTemplateService.syncTemplateToRedis(json);
        return ResponseResult.success();
    }

    /**
     * 查询所有模板信息接口
     *
     * @return ResponseResult<Map < String, String>> 查询结果，包含所有模板信息
     * <AUTHOR> Lab
     * @since 1.0.0
     */
    @GetMapping("/list")
    @Operation(
            summary = "查询所有模板信息接口",
            description = "通过Redis Hash的scan命令查询所有卡片模板信息"
    )
    public ResponseResult<Map<String, JSONObject>> getAllTemplates() {
        Map<String, JSONObject> templates = cardTemplateService.getAllTemplates();
        return ResponseResult.success("查询所有模板信息成功", templates);
    }

    /**
     * 删除指定模板接口
     *
     * @param templateCode 模板编码
     * @return ResponseResult<Boolean> 删除结果，true表示删除成功，false表示模板不存在
     * <AUTHOR> Lab
     * @since 1.0.0
     */
    @GetMapping("/delete/{templateCode}")
    @Operation(
            summary = "删除指定模板接口",
            description = "根据模板编码删除指定的卡片模板，删除前会先检查模板是否存在"
    )
    public ResponseResult<Boolean> deleteTemplate(@PathVariable String templateCode) {
        if (templateCode == null || templateCode.trim().isEmpty()) {
            return ResponseResult.error(400, "模板编码不能为空");
        }

        boolean result = cardTemplateService.deleteTemplate(templateCode.trim());
        String message = result ? "模板删除成功" : "模板不存在，无法删除";
        return ResponseResult.success(message, result);
    }

}
