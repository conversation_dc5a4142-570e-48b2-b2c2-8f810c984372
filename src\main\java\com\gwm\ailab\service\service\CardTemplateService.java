package com.gwm.ailab.service.service;

import com.alibaba.fastjson2.JSONObject;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.Cursor;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ScanOptions;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

/**
 * 卡片服务类
 *
 * <AUTHOR> Lab
 * @version 1.0.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class CardTemplateService {

    private final RedisTemplate<String, String> redisTemplate;


    public void syncTemplateToRedis(JSONObject json) {
        redisTemplate.opsForHash().put("service:card:template", json.getString("code"), json.getString("content"));
        log.warn("接收模板同步信息, key:【{}】, hashkey：【{}】, value:【{}】", "service:card:template", json.getString("code"), json.getString("content"));
    }

    /**
     * 通过hashkey的scan查询所有模板信息
     *
     * @return Map<String, String> 所有模板信息，key为模板编码，value为模板内容
     * <AUTHOR> Lab
     * @since 1.0.0
     */
    public Map<String, JSONObject> getAllTemplates() {
        String hashKey = "service:card:template";
        Map<String, JSONObject> templates = new HashMap<>();

        try {
            // 使用scan命令遍历hash中的所有字段
            ScanOptions scanOptions = ScanOptions.scanOptions().count(100).build();
            Cursor<Map.Entry<Object, Object>> cursor = redisTemplate.opsForHash().scan(hashKey, scanOptions);

            while (cursor.hasNext()) {
                Map.Entry<Object, Object> entry = cursor.next();
                String templateCode = entry.getKey().toString();
                String templateContent = entry.getValue().toString();
                JSONObject content = JSONObject.parseObject(templateContent);
                templates.put(templateCode, content);
            }

            cursor.close();
            log.warn("查询所有模板信息成功，共查询到 {} 个模板", templates.size());

        } catch (Exception e) {
            log.error("查询所有模板信息失败", e);
            throw new RuntimeException("查询模板信息失败: " + e.getMessage());
        }

        return templates;
    }

    /**
     * 通过hashkey删除指定的模板
     *
     * @param templateCode 模板编码
     * @return boolean 删除结果，true表示删除成功，false表示模板不存在
     * <AUTHOR> Lab
     * @since 1.0.0
     */
    public boolean deleteTemplate(String templateCode) {
        String hashKey = "service:card:template";

        try {
            // 先判断是否有该key
            Boolean exists = redisTemplate.opsForHash().hasKey(hashKey, templateCode);

            if (Boolean.TRUE.equals(exists)) {
                // 如果存在则删除
                Long deletedCount = redisTemplate.opsForHash().delete(hashKey, templateCode);
                boolean success = deletedCount > 0;

                if (success) {
                    log.warn("删除模板成功，模板编码：{}", templateCode);
                } else {
                    log.warn("删除模板失败，模板编码：{}", templateCode);
                }

                return success;
            } else {
                log.warn("模板不存在，无法删除，模板编码：{}", templateCode);
                return false;
            }

        } catch (Exception e) {
            log.error("删除模板失败，模板编码：{}", templateCode, e);
            throw new RuntimeException("删除模板失败: " + e.getMessage());
        }
    }

}
