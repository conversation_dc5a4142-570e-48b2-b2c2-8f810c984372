package com.gwm.ailab.service.aspect;

import com.alibaba.fastjson2.JSON;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.*;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;


/**
 * API日志切面
 * 记录接口调用信息：入参、出参、接口链接、调用方地址等
 * 
 * <AUTHOR> Lab
 * @version 1.0.0
 */
@Slf4j
@Aspect
@Component
public class ApiLogAspect {

    /**
     * 定义切点：拦截所有Controller层的方法
     */
    @Pointcut("execution(* com.gwm.ailab.service.controller..*.*(..))")
    public void apiPointcut() {
    }

    /**
     * 环绕通知：记录接口调用的完整信息
     */
    @Around("apiPointcut()")
    public Object around(ProceedingJoinPoint joinPoint) throws Throwable {
        long startTime = System.currentTimeMillis();

        // 获取请求信息
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        HttpServletRequest request = attributes != null ? attributes.getRequest() : null;

        Object result = null;
        Exception exception = null;

        try {
            // 执行目标方法
            result = joinPoint.proceed();
            return result;
        } catch (Exception e) {
            exception = e;
            throw e;
        } finally {
            // 计算执行时间
            long endTime = System.currentTimeMillis();
            long executionTime = endTime - startTime;

            // 记录一行日志
            logApiCall(joinPoint, request, result, exception, executionTime);
        }
    }

    /**
     * 记录API调用的单行日志
     */
    private void logApiCall(JoinPoint joinPoint, HttpServletRequest request, Object result, Exception exception, long executionTime) {
        StringBuilder logBuilder = new StringBuilder();

        // 基本信息
        String method = request != null ? request.getMethod() : "UNKNOWN";
        String uri = request != null ? request.getRequestURI() : "UNKNOWN";
        String clientIp = request != null ? getClientIpAddress(request) : "UNKNOWN";
        String className = joinPoint.getTarget().getClass().getSimpleName();
        String methodName = joinPoint.getSignature().getName();

        // 构建日志信息
        logBuilder.append("API_CALL | ");
        logBuilder.append("Method=").append(method).append(" | ");
        logBuilder.append("URI=").append(uri).append(" | ");
        logBuilder.append("Class=").append(className).append(".").append(methodName).append(" | ");
        logBuilder.append("ClientIP=").append(clientIp).append(" | ");
        logBuilder.append("Duration=").append(executionTime).append("ms | ");

        // 请求参数（简化）
        Object[] args = joinPoint.getArgs();
        if (args != null && args.length > 0) {
            try {
                String argsJson = JSON.toJSONString(args);
                // 限制参数长度，避免日志过长
                if (argsJson.length() > 200) {
                    argsJson = argsJson.substring(0, 200) + "...";
                }
                logBuilder.append("Args=").append(argsJson).append(" | ");
            } catch (Exception e) {
                logBuilder.append("Args=[SERIALIZE_ERROR] | ");
            }
        }

        // 结果状态
        if (exception != null) {
            logBuilder.append("Status=FAILED | ");
            logBuilder.append("Error=").append(exception.getClass().getSimpleName()).append(":").append(exception.getMessage());
        } else {
            logBuilder.append("Status=SUCCESS");
            if (result != null) {
                try {
                    String resultJson = JSON.toJSONString(result);
                    // 限制结果长度
                    if (resultJson.length() > 200) {
                        resultJson = resultJson.substring(0, 200) + "...";
                    }
                    logBuilder.append(" | Result=").append(resultJson);
                } catch (Exception e) {
                    logBuilder.append(" | Result=[SERIALIZE_ERROR]");
                }
            }
        }

        // 输出单行warn级别日志
        log.warn(logBuilder.toString());
    }

    /**
     * 获取客户端真实IP地址
     */
    private String getClientIpAddress(HttpServletRequest request) {
        String[] headerNames = {
            "X-Forwarded-For",
            "X-Real-IP",
            "Proxy-Client-IP",
            "WL-Proxy-Client-IP",
            "HTTP_CLIENT_IP",
            "HTTP_X_FORWARDED_FOR"
        };
        
        for (String headerName : headerNames) {
            String ip = request.getHeader(headerName);
            if (ip != null && !ip.isEmpty() && !"unknown".equalsIgnoreCase(ip)) {
                // 多级代理的情况，第一个IP为客户端真实IP
                if (ip.contains(",")) {
                    ip = ip.split(",")[0].trim();
                }
                return ip;
            }
        }
        
        return request.getRemoteAddr();
    }

}
